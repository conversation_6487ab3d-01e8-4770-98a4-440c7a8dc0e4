import { useEffect, useMemo, useState } from 'react';

import generateLinkShare from '@/application/conversation/generateLinkShare';
import transformLinkUri from '@/helpers/transformLinkUri';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import { getMessagesByConversationId } from '@/services/message.service';

import ChatListMessages from '../Chatbot/ChatListMessages';
import DButton from '../Global/DButton';
import DModal from '../Global/DModal';
import CopyIcon from '../Global/Icons/CopyIcon';

const ModalShareConversation = ({ open, onClose, conversation_id }) => {
  console.log('ModalShareConversation props:', { open, conversation_id });

  const memoizedConversationId = useMemo(
    () => conversation_id,
    [conversation_id]
  );
  const [messages, setMessages] = useState([]);
  const { data: messagesRaw, isLoading: messagesLoading } = useDanteApi(
    getMessagesByConversationId,
    [],
    {},
    memoizedConversationId
  );

  const [loadingLink, setLoadingLink] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);
  const [error, setError] = useState(null);

  const handleCopyLink = async () => {
    console.log('handleCopyLink called with conversation_id:', conversation_id);

    if (!conversation_id) {
      setError('No conversation selected');
      return;
    }

    try {
      setLoadingLink(true);
      setError(null);

      console.log('Generating share link for conversation:', conversation_id);

      // Generate the shareable link
      const linkPathname = await generateLinkShare(conversation_id);
      const link = `${window.location.origin}${linkPathname}`;

      console.log('Generated link:', link);

      // Copy the link to clipboard with fallback
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(link);
        console.log('Link copied using navigator.clipboard');
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = link;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        console.log('Link copied using fallback method');
      }

      // Show success feedback
      setLinkCopied(true);
      console.log('Copy success feedback shown');
      setTimeout(() => {
        setLinkCopied(false);
      }, 3000);

    } catch (error) {
      console.error('Error copying link:', error);
      setError(`Failed to copy link: ${error.message || 'Please try again.'}`);
      setTimeout(() => {
        setError(null);
      }, 5000);
    } finally {
      setLoadingLink(false);
    }
  };

  useEffect(() => {
    if (messagesRaw?.results) {
      const processedMessages = [];

      messagesRaw.results.forEach((message) => {
        const transformedMessages = transformMessageToBubbleMessage(message);
        processedMessages.push(...transformedMessages);
      });

      setMessages(processedMessages);
    }
  }, [messagesRaw]);


  return (
    <DModal
      isOpen={open}
      onClose={onClose}
      title="Share your chat with a link"
      subtitle="Messages sent after creating the link won't be shared. The link allows anyone to view this chat."
      footer={
        <div className="w-full">
          {error && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
              {error}
            </div>
          )}
          <DButton
            variant="dark"
            fullWidth
            onClick={handleCopyLink}
            loading={loadingLink}
            disabled={!conversation_id}
          >
            <CopyIcon />
            {linkCopied ? 'Copied!' : 'Copy link'}
          </DButton>
        </div>
      }    >
      <div className='h-[400px] overflow-y-auto'>
        <ChatListMessages
          transformLinkUri={transformLinkUri}
          messages={messages}
          readonly
          hideFooter
          sources={[]}
          openSources={false}
          setOpenSources={() => {}}
          showSources={[]}
          sourcesLoading={[]}
        />
      </div>
    </DModal>
  );
};

export default ModalShareConversation;
